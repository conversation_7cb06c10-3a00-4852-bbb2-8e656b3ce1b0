#!/usr/bin/env zsh

# Script to upload SSH public key to all pi hosts defined in inventory.yml
# Usage: ./upload-key.sh

set -e  # Exit on any error

# Configuration
SSH_KEY_FILE="$HOME/.ssh/id_ed25519.pub"
INVENTORY_FILE="inventory.yml"
SSH_USER="ryan"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check if SSH key file exists
if [[ ! -f "$SSH_KEY_FILE" ]]; then
    print_status $RED "Error: SSH public key not found at $SSH_KEY_FILE"
    exit 1
fi

# Check if inventory file exists
if [[ ! -f "$INVENTORY_FILE" ]]; then
    print_status $RED "Error: Inventory file not found at $INVENTORY_FILE"
    exit 1
fi

print_status $YELLOW "Uploading SSH key from $SSH_KEY_FILE to pi hosts..."
echo

# Function to extract hostnames from inventory.yml
get_pi_hosts() {
    # Try using yq if available (more robust YAML parsing)
    if command -v yq >/dev/null 2>&1; then
        yq eval '.pis.hosts | keys | .[]' "$INVENTORY_FILE" 2>/dev/null
    else
        # Fallback to grep/awk parsing
        # Extract hostnames under pis.hosts section
        awk '
        /^pis:/ { in_pis=1; next }
        /^[[:space:]]*hosts:/ && in_pis { in_hosts=1; next }
        /^[[:space:]]*[a-zA-Z]/ && in_hosts && in_pis {
            if ($0 ~ /^[[:space:]]*[a-zA-Z0-9.-]+:/) {
                gsub(/^[[:space:]]*/, "")
                gsub(/:.*$/, "")
                print $0
            }
        }
        /^[a-zA-Z]/ && !/^pis:/ { in_pis=0; in_hosts=0 }
        ' "$INVENTORY_FILE"
    fi
}

# Get list of pi hosts
hosts=($(get_pi_hosts))

if [[ ${#hosts[@]} -eq 0 ]]; then
    print_status $RED "Error: No pi hosts found in $INVENTORY_FILE"
    exit 1
fi

print_status $YELLOW "Found ${#hosts[@]} pi hosts:"
for host in "${hosts[@]}"; do
    echo "  - $host"
done
echo

# Upload SSH key to each host
success_count=0
failed_hosts=()

for host in "${hosts[@]}"; do
    print_status $YELLOW "Uploading key to $host..."

    if ssh-copy-id -i "$SSH_KEY_FILE" "$SSH_USER@$host" >/dev/null 2>&1; then
        print_status $GREEN "✓ Successfully uploaded key to $host"
        ((success_count++))
    else
        print_status $RED "✗ Failed to upload key to $host"
        failed_hosts+=("$host")
    fi
done

echo
print_status $YELLOW "Summary:"
print_status $GREEN "Successfully uploaded to $success_count/${#hosts[@]} hosts"

if [[ ${#failed_hosts[@]} -gt 0 ]]; then
    print_status $RED "Failed hosts:"
    for host in "${failed_hosts[@]}"; do
        echo "  - $host"
    done
    echo
    print_status $YELLOW "For failed hosts, you may need to:"
    echo "  1. Ensure the host is reachable"
    echo "  2. Verify SSH access with password authentication"
    echo "  3. Check if the user '$SSH_USER' exists on the remote host"
    exit 1
else
    print_status $GREEN "All hosts updated successfully!"
fi
